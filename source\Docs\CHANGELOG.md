
# v1
## 1.2.0

| <div align="center"><h3>🌟Features</h3></div> | <div align="center"><h3 dir="rtl">🌟‏الميزات</h3></div> |
| :--- | ---: |
| - | <div dir="rtl">-</div> |
| <div align="center"><h3>💪Fixes</h3></div> | <div align="center"><h3 dir="rtl">💪‏الإصلاحات</h3></div> |
| - | <div dir="rtl">-</div> |
| <div align="center"><h3>🛠️Behind the scenes</h3></div> | <div align="center"><h3 dir="rtl">🛠️‏خَلفَ الكَواليس</h3></div> |
| - | <div dir="rtl">-</div> |

## 1.1.0

| <div align="center"><h3>🌟Features</h3></div> | <div align="center"><h3 dir="rtl">🌟‏الميزات</h3></div> |
| :--- | ---: |
| Added support of manually inserting tashkeel, with uppercase `A`, `O` & `I` | <div dir="rtl">تَمَّت إضافةُ دَعمٍ لِإدخالِ التَّشكيلِ يَدَوِيًّا، بِاستِخدامِ الأحرفِ الكَبيرةِ `A` و `O` و `I`</div> |
| Added new non-arabic letters like `ڠ` with `G`, `پ` with `P`, `ڤ` with `V` | <div dir="rtl">تَمَّت إضافةُ أحرفٍ غَيرِ عَرَبِيّةٍ جَديدةٍ مِثلَ `ڠ` بِواسطةِ `G`، و `پ` بِواسطةِ `P`، و `ڤ` بِواسطةِ `V`</div> |
| Added support of manually inserting shadda with `E` | <div dir="rtl">تَمَّت إضافةُ دَعمٍ لِإدخالِ الشَّدّةِ يَدَوِيًّا بِواسطةِ `E`</div> |
| Assigned a key for `ض` directly, with `p` | <div dir="rtl">تَمَّ تَخصيصُ مِفتاحٍ لِحَرفِ `ض` مُباشَرة، بِواسطةِ `p`</div> |
| Added `u` as another way to insert `ُ` (damma) | <div dir="rtl">تَمَّت إضافةُ `u` كَطَريقةٍ أُخرى لِإدخالِ `ُ` (الضَّمّة)</div> |
| <div align="center"><h3>🛠️Behind the scenes</h3></div> | <div align="center"><h3 dir="rtl">🛠️‏خَلفَ الكَواليس</h3></div> |
| Bumped the keyman version to v17, as the new standard for the `Arabiyy` tool | <div dir="rtl">تَمَّ تَحديثُ إصدارِ Keyman إلى v17، كالمِعيارِ الجَديدِ لأداةِ `Arabiyy`</div> |
| Temporary: Made `)` actually output `()` instead because there's currently no way to insert `)` | <div dir="rtl">مُؤقَّتًا: جُعِلَ `)` يُنتِجُ `()` بِدَلًا مِن ذَلِكَ لِأنَّهُ لا يوجَدُ حالِيًّا طَريقةٌ لإدخالِ `)`</div> |
| Made the `٪` sign the default output when pressing `%` | <div dir="rtl">جُعِلَت إشارةُ `٪` هي الإخراجَ الافتراضيَّ عِندَ الضَّغطِ على `%`</div> |
| Made the `*` sign the default output when pressing `µ` in `NumOnShift` keyboard (It was missing, needed for writing markdown) | <div dir="rtl">جُعِلَت إشارةُ `*` هي الإخراجَ الافتراضيَّ عِندَ الضَّغطِ على `µ` في لَوحةِ مَفاتيحِ `NumOnShift` (كانَت مَفقودة، ومَطلوبةً لِكِتابةِ Markdown)</div> |

## 1.0.0
- Initial release
- Basic functionality
- Support for Windows, macOS, and Linux
- Basic documentation
- Basic support for numbers on shift

# Template
## a.x.y

<div align="center"><h1>Changes - التَّغَيُّرات</h1></div>

| <div align="center"><h3>🌟Features</h3></div> | <div align="center"><h3 dir="rtl">🌟‏الميزات</h3></div> |
| :--- | ---: |
| - | <div dir="rtl">-</div> |
| <div align="center"><h3>💪Fixes</h3></div> | <div align="center"><h3 dir="rtl">💪‏الإصلاحات</h3></div> |
| - | <div dir="rtl">-</div> |
| <div align="center"><h3>🛠️Behind the scenes</h3></div> | <div align="center"><h3 dir="rtl">🛠️‏خَلفَ الكَواليس</h3></div> |
| - | <div dir="rtl">-</div> |