if(typeof keyman === 'undefined') {console.log('Keyboard requires KeymanWeb 10.0 or later');if(typeof tavultesoft !== 'undefined') tavultesoft.keymanweb.util.alert("This keyboard requires KeymanWeb 10.0 or later");} else {KeymanWeb.KR(new Keyboard_arabiyynumonshift());}function Keyboard_arabiyynumonshift(){this._v=(typeof keyman!="undefined"&&typeof keyman.version=="string")?parseInt(keyman.version,10):9;this.KI="Keyboard_arabiyynumonshift";this.KN="Arabiyy - عَرَبِيّ";this.KMINVER="17.0";this.KV=null;this.KDU=0;this.KH='';this.KM=1;this.KBVER="1.1.0";this.KMBM=0x10;this.KRTL=1;this.s11="ذظخحضصىطمنبثشزلكجهغفدسعترق";this.s12="اوي";this.s13="ذظخحضصىطمنبثشزلكجهغفدسعترقاوي";this.s14="ًٌٍ";this.s15="َُِّ";this.s16="َأا";this.s17="ةًٌٍَُِّأءاويذظخحضصىطمنبثشزلكجهغفدسعترق";this.s20="/\\";this.s21="ۚۖۗۙۛ۞۝۩ۣۡۢۤۥۦۧۨۘۖۜ";this.s22="ۖۗۙۛ۞۝۩ۣۡۢۤۥۦۧۨۘۖۜۚ";this.s23=["ب","ف","و","ك","أ",''];this.s24=" ،؛:!.؟";this.s25="دتسضك";this.s26="ذثشظخ";this.s27="َاآأٰىع";this.s28="اآأٰىعَ";this.s29="دذضظ";this.s30="ذضظد";this.s31="حخه";this.s32="خهح";this.s33="سصش";this.s34="صشس";this.s35="تةطث";this.s36="ةطثت";this.s37="َُِ";this.KVER="18.0.238.0";this.KVS=[];this.gs=function(t,e) {return this.g0(t,e);};this.gs=function(t,e) {return this.g0(t,e);};this.g0=function(t,e) {var k=KeymanWeb,r=0,m=0;if(k.KKM(e,16384,32)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t," ");}}else if(k.KKM(e,16400,33)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"!");}}else if(k.KKM(e,16400,34)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ع");}}else if(k.KKM(e,16400,37)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"٪");}}else if(k.KKM(e,16400,38)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16384,39)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"'");}}else if(k.KKM(e,16400,40)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"خ");}}else if(k.KKM(e,16400,41)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"()");}}else if(k.KKM(e,16400,42)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ۚ");}}else if(k.KKM(e,16400,43)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"+");}}else if(k.KKM(e,16384,44)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"،");}}else if(k.KKM(e,16384,45)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ط");}}else if(k.KKM(e,16384,46)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,".");}}else if(k.KKM(e,16384,47)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"/");}}else if(k.KKM(e,16384,48)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"0");}}else if(k.KKM(e,16384,49)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"1");}}else if(k.KKM(e,16384,50)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"2");}}else if(k.KKM(e,16384,51)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"3");}}else if(k.KKM(e,16384,52)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"4");}}else if(k.KKM(e,16384,53)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"5");}}else if(k.KKM(e,16384,54)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"6");}}else if(k.KKM(e,16384,55)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"7");}}else if(k.KKM(e,16384,56)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"8");}}else if(k.KKM(e,16384,57)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"9");}}else if(k.KKM(e,16400,58)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,":");}}else if(k.KKM(e,16384,59)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"؛");}}else if(k.KKM(e,16384,61)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"=");}}else if(k.KKM(e,16400,63)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"؟");}}else if(k.KKM(e,16400,65)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"َ ");}}else if(k.KKM(e,16400,66)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16400,67)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"{");}}else if(k.KKM(e,16400,68)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ض");}}else if(k.KKM(e,16400,69)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ّ");}}else if(k.KKM(e,16400,70)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ڤ");}}else if(k.KKM(e,16400,71)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ڠ");}}else if(k.KKM(e,16400,72)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16400,73)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ِ ");}}else if(k.KKM(e,16400,74)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ڠ");}}else if(k.KKM(e,16400,75)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16400,76)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16400,77)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16400,78)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16400,79)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ُ ");}}else if(k.KKM(e,16400,80)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"پ");}}else if(k.KKM(e,16400,81)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16400,82)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16400,83)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ص");}}else if(k.KKM(e,16400,84)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ط");}}else if(k.KKM(e,16400,85)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ُ ");}}else if(k.KKM(e,16400,86)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ڤ");}}else if(k.KKM(e,16400,87)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"[");}}else if(k.KKM(e,16400,88)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"]");}}else if(k.KKM(e,16400,89)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16400,90)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16400,95)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}}else if(k.KKM(e,16384,97)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"َ");}}else if(k.KKM(e,16384,98)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ب");}}else if(k.KKM(e,16384,99)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ص");}}else if(k.KKM(e,16384,100)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"د");}}else if(k.KKM(e,16384,101)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ْ");}}else if(k.KKM(e,16384,102)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ف");}}else if(k.KKM(e,16384,103)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"غ");}}else if(k.KKM(e,16384,104)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ه");}}else if(k.KKM(e,16384,105)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ِ");}}else if(k.KKM(e,16384,106)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ج");}}else if(k.KKM(e,16384,107)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ك");}}else if(k.KKM(e,16384,108)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ل");}}else if(k.KKM(e,16384,109)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"م");}}else if(k.KKM(e,16384,110)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ن");}}else if(k.KKM(e,16384,111)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ُ");}}else if(k.KKM(e,16384,112)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ض");}}else if(k.KKM(e,16384,113)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ق");}}else if(k.KKM(e,16384,114)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ر");}}else if(k.KKM(e,16384,115)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"س");}}else if(k.KKM(e,16384,116)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ت");}}else if(k.KKM(e,16384,117)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ُ");}}else if(k.KKM(e,16384,118)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ى");}}else if(k.KKM(e,16384,119)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"و");}}else if(k.KKM(e,16384,120)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ش");}}else if(k.KKM(e,16384,121)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ي");}}else if(k.KKM(e,16384,122)) {if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ز");}}else if(k.KKM(e,16384,167)) {if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}else if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"°");}else if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"/");}else if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"*");}else if(1){r=m=1;k.KDC(0,t);k.KDO(-1,t,0);}else if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ق");}else if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ح");}else if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"ء");}else if(1){r=m=1;k.KDC(0,t);k.KO(-1,t,"/");}}if(m==1) {k.KDC(-1,t);r=this.g1(t,e);m=2;}return r;};this.g1=function(t,e) {var k=KeymanWeb,r=1,m=0;if(k.KFCM(10,t,[{t:'a',a:this.s20},'ب','ِ','س','م','ِ','ل','ّ','َ','ه'])){m=1;k.KDC(10,t);k.KO(-1,t,"بِسْمِ اللَّهِ الرَّحْمَـٰنِ الرَّحِيم");}else if(k.KFCM(9,t,['ب','ِ','س','م','ِ','ل','ّ','َ','ه'])){m=1;k.KDC(9,t);k.KO(-1,t,"بِسمِ اللَّه");}else if(k.KFCM(8,t,['ا','ل','ل','َ','ذ','َ','ي','ن'])){m=1;k.KDC(8,t);k.KO(-1,t,"اللَّذَين");}else if(k.KFCM(8,t,['ا','ل','ل','َ','ت','َ','ي','ن'])){m=1;k.KDC(8,t);k.KO(-1,t,"اللَّتَين");}else if(k.KFCM(7,t,['ا','ل','ل','َ','ت','ِ','ِ'])){m=1;k.KDC(7,t);k.KO(-1,t,"الَّتي");}else if(k.KFCM(7,t,['ا','ل','ل','َ','ذ','ِ','ِ'])){m=1;k.KDC(7,t);k.KO(-1,t,"الَّذي");}else if(k.KFCM(7,t,['ا','ل','ل','َ','ذ','ا','ن'])){m=1;k.KDC(7,t);k.KO(-1,t,"اللَّذان");}else if(k.KFCM(7,t,['ا','ل','ل','َ','ت','ا','ن'])){m=1;k.KDC(7,t);k.KO(-1,t,"اللَّتان");}else if(k.KFCM(7,t,['ا','ل','ل','ا','ت','ِ','ِ'])){m=1;k.KDC(7,t);k.KO(-1,t,"اللّاتي");}else if(k.KFCM(7,t,['ا','ل','ل','ا','ء','ِ','ِ'])){m=1;k.KDC(7,t);k.KO(-1,t,"اللّائي");}else if(k.KFCM(7,t,['ه','ا','ؤ','ُ','ل','ا','ء'])){m=1;k.KDC(7,t);k.KO(-1,t,"هَـٰؤُلاء");}else if(k.KFCM(7,t,['أ','و','ل','ا','ء','ِ','ك'])){m=1;k.KDC(7,t);k.KO(-1,t,"أولَـٰئِك");}else if(k.KFCM(6,t,[{t:'a',a:this.s23},'َ','ل',{t:'a',a:this.s15},'ْ',{t:'a',a:this.s11}])){m=1;k.KDC(6,t);k.KIO(-1,this.s23,1,t);k.KO(-1,t,"َل");k.KIO(-1,this.s15,4,t);k.KO(-1,t,"ل");k.KIO(-1,this.s11,6,t);k.KO(-1,t,"ّ");}else if(k.KFCM(6,t,[{t:'n'},{t:'a',a:this.s23},{t:'a',a:this.s15},'ل','َ',{t:'a',a:this.s24}])){m=1;k.KDC(5,t);k.KIO(-1,this.s23,2,t);k.KIO(-1,this.s15,3,t);k.KO(-1,t,"لا");k.KIO(-1,this.s24,6,t);}else if(k.KFCM(6,t,[' ',{t:'a',a:this.s23},{t:'a',a:this.s15},'ل','َ',{t:'a',a:this.s24}])){m=1;k.KDC(6,t);k.KO(-1,t," ");k.KIO(-1,this.s23,2,t);k.KIO(-1,this.s15,3,t);k.KO(-1,t,"لا");k.KIO(-1,this.s24,6,t);}else if(k.KFCM(6,t,[{t:'n'},{t:'a',a:this.s23},{t:'a',a:this.s15},'ف','ِ',' '])){m=1;k.KDC(5,t);k.KIO(-1,this.s23,2,t);k.KIO(-1,this.s15,3,t);k.KO(-1,t,"في ");}else if(k.KFCM(6,t,[' ',{t:'a',a:this.s23},{t:'a',a:this.s15},'ف','ِ',' '])){m=1;k.KDC(6,t);k.KO(-1,t," ");k.KIO(-1,this.s23,2,t);k.KIO(-1,this.s15,3,t);k.KO(-1,t,"في ");}else if(k.KFCM(6,t,[{t:'n'},{t:'a',a:this.s23},{t:'a',a:this.s15},'م','َ',' '])){m=1;k.KDC(5,t);k.KIO(-1,this.s23,2,t);k.KIO(-1,this.s15,3,t);k.KO(-1,t,"ما ");}else if(k.KFCM(6,t,[' ',{t:'a',a:this.s23},{t:'a',a:this.s15},'م','َ',' '])){m=1;k.KDC(6,t);k.KO(-1,t," ");k.KIO(-1,this.s23,2,t);k.KIO(-1,this.s15,3,t);k.KO(-1,t,"ما ");}else if(k.KFCM(6,t,['ا','ل','ل','َ','ت','ي'])){m=1;k.KDC(6,t);k.KO(-1,t,"الَّتي");}else if(k.KFCM(6,t,['ا','ل','ل','َ','ذ','ي'])){m=1;k.KDC(6,t);k.KO(-1,t,"الَّذي");}else if(k.KFCM(6,t,['ا','ل','ل','ا','ت','ي'])){m=1;k.KDC(6,t);k.KO(-1,t,"اللّاتي");}else if(k.KFCM(6,t,['ا','ل','ل','ا','ء','ي'])){m=1;k.KDC(6,t);k.KO(-1,t,"اللّائي");}else if(k.KFCM(5,t,['َ','ل','ّ','َ','ه'])){m=1;k.KDC(5,t);k.KO(-1,t,"اللَّه");}else if(k.KFCM(5,t,['أ','ل','ّ','َ','ه'])){m=1;k.KDC(5,t);k.KO(-1,t,"اللَّه");}else if(k.KFCM(5,t,['ا','ل','ّ','َ','ه'])){m=1;k.KDC(5,t);k.KO(-1,t,"اللَّه");}else if(k.KFCM(5,t,['ا','ل','ل','َ','ه'])){m=1;k.KDC(5,t);k.KO(-1,t,"اللَّه");}else if(k.KFCM(5,t,[{t:'a',a:this.s20},'أ','ز','و','ج'])){m=1;k.KDC(5,t);k.KO(-1,t,"عَزَّ وَجَلّ");}else if(k.KFCM(5,t,[{t:'a',a:this.s20},'َ','ز','و','ج'])){m=1;k.KDC(5,t);k.KO(-1,t,"عَزَّ وَجَلّ");}else if(k.KFCM(5,t,[{t:'a',a:this.s20},'ع','ز','و','ج'])){m=1;k.KDC(5,t);k.KO(-1,t,"عَزَّ وَجَلّ");}else if(k.KFCM(5,t,['ا','ل','أ','ُ','ُ'])){m=1;k.KDC(5,t);k.KO(-1,t,"الأو");}else if(k.KFCM(5,t,[{t:'n'},'ل',{t:'a',a:this.s15},'ْ',{t:'a',a:this.s11}])){m=1;k.KDC(4,t);k.KO(-1,t,"ل");k.KIO(-1,this.s15,3,t);k.KO(-1,t,"ل");k.KIO(-1,this.s11,5,t);k.KO(-1,t,"ّ");}else if(k.KFCM(5,t,[' ','ل',{t:'a',a:this.s15},'ْ',{t:'a',a:this.s11}])){m=1;k.KDC(5,t);k.KO(-1,t," ل");k.KIO(-1,this.s15,3,t);k.KO(-1,t,"ل");k.KIO(-1,this.s11,5,t);k.KO(-1,t,"ّ");}else if(k.KFCM(5,t,[{t:'a',a:this.s23},'ل',{t:'a',a:this.s15},'ْ',{t:'a',a:this.s11}])){m=1;k.KDC(5,t);k.KIO(-1,this.s23,1,t);k.KO(-1,t,"ل");k.KIO(-1,this.s15,3,t);k.KO(-1,t,"ل");k.KIO(-1,this.s11,5,t);k.KO(-1,t,"ّ");}else if(k.KFCM(5,t,['ل','ِ','ل','ء','َ'])){m=1;k.KDC(5,t);k.KO(-1,t,"لِلأ");}else if(k.KFCM(5,t,['ل','َ','ل','ء','َ'])){m=1;k.KDC(5,t);k.KO(-1,t,"لَلأ");}else if(k.KFCM(5,t,['ل','ِ','ل','ء','ُ'])){m=1;k.KDC(5,t);k.KO(-1,t,"لِلأُ");}else if(k.KFCM(5,t,['ل','َ','ل','ء','ُ'])){m=1;k.KDC(5,t);k.KO(-1,t,"لَلأُ");}else if(k.KFCM(5,t,['ل','ِ','ل','ء','ِ'])){m=1;k.KDC(5,t);k.KO(-1,t,"لِلإ");}else if(k.KFCM(5,t,['ل','َ','ل','ء','ِ'])){m=1;k.KDC(5,t);k.KO(-1,t,"لَلإ");}else if(k.KFCM(5,t,['ب','ِ','ل','ّ','ل'])){m=1;k.KDC(5,t);k.KO(-1,t,"بِاللّ");}else if(k.KFCM(5,t,[' ',{t:'a',a:this.s23},'َ','ّ',{t:'a',a:this.s11}])){m=1;k.KDC(5,t);k.KO(-1,t," ");k.KIO(-1,this.s23,2,t);k.KO(-1,t,"ا");k.KIO(-1,this.s11,5,t);}else if(k.KFCM(5,t,[{t:'n'},{t:'a',a:this.s23},'َ','ّ',{t:'a',a:this.s11}])){m=1;k.KDC(4,t);k.KIO(-1,this.s23,2,t);k.KO(-1,t,"ا");k.KIO(-1,this.s11,5,t);}else if(k.KFCM(5,t,[' ',{t:'a',a:this.s23},'ِ','ّ',{t:'a',a:this.s11}])){m=1;k.KDC(5,t);k.KO(-1,t," ");k.KIO(-1,this.s23,2,t);k.KO(-1,t,"ِا");k.KIO(-1,this.s11,5,t);}else if(k.KFCM(5,t,[{t:'n'},{t:'a',a:this.s23},'ِ','ّ',{t:'a',a:this.s11}])){m=1;k.KDC(4,t);k.KIO(-1,this.s23,2,t);k.KO(-1,t,"ِا");k.KIO(-1,this.s11,5,t);}else if(k.KFCM(5,t,['ت','َ','ن','ّ','ْ'])){m=1;k.KDC(5,t);k.KO(-1,t,"ةً");}else if(k.KFCM(5,t,['ت','ُ','ن','ّ','ْ'])){m=1;k.KDC(5,t);k.KO(-1,t,"ةٌ");}else if(k.KFCM(5,t,['ت','ِ','ن','ّ','ْ'])){m=1;k.KDC(5,t);k.KO(-1,t,"ةٍ");}else if(k.KFCM(5,t,[{t:'a',a:this.s11},'ّ','َ','ن','ْ'])){m=1;k.KDC(5,t);k.KIO(-1,this.s11,1,t);k.KO(-1,t,"ًّا");}else if(k.KFCM(5,t,['و','ّ','َ','ن','ْ'])){m=1;k.KDC(5,t);k.KO(-1,t,"وًّا");}else if(k.KFCM(5,t,['ي','ّ','َ','ن','ْ'])){m=1;k.KDC(5,t);k.KO(-1,t,"يًّا");}else if(k.KFCM(5,t,[{t:'n'},{t:'a',a:this.s23},'ل','َ',{t:'a',a:this.s24}])){m=1;k.KDC(4,t);k.KIO(-1,this.s23,2,t);k.KO(-1,t,"لا");k.KIO(-1,this.s24,5,t);}else if(k.KFCM(5,t,[' ',{t:'a',a:this.s23},'ل','َ',{t:'a',a:this.s24}])){m=1;k.KDC(5,t);k.KO(-1,t," ");k.KIO(-1,this.s23,2,t);k.KO(-1,t,"لا");k.KIO(-1,this.s24,5,t);}else if(k.KFCM(5,t,['إ','ل','ّ','َ',' '])){m=1;k.KDC(5,t);k.KO(-1,t,"إلّا ");}else if(k.KFCM(5,t,['و','ِ','ل','ّ','َ'])){m=1;k.KDC(5,t);k.KO(-1,t,"وإلّا");}else if(k.KFCM(5,t,[{t:'n'},'ف','ّ','ِ',' '])){m=1;k.KDC(4,t);k.KO(-1,t,"فَفي ");}else if(k.KFCM(5,t,[' ','ف','ّ','ِ',' '])){m=1;k.KDC(5,t);k.KO(-1,t," فَفي ");}else if(k.KFCM(5,t,[{t:'n'},{t:'a',a:this.s23},'ف','ِ',' '])){m=1;k.KDC(4,t);k.KIO(-1,this.s23,2,t);k.KO(-1,t,"في ");}else if(k.KFCM(5,t,[' ',{t:'a',a:this.s23},'ف','ِ',' '])){m=1;k.KDC(5,t);k.KO(-1,t," ");k.KIO(-1,this.s23,2,t);k.KO(-1,t,"في ");}else if(k.KFCM(5,t,[{t:'n'},{t:'a',a:this.s23},'م','َ',' '])){m=1;k.KDC(4,t);k.KIO(-1,this.s23,2,t);k.KO(-1,t,"ما ");}else if(k.KFCM(5,t,[' ',{t:'a',a:this.s23},'م','َ',' '])){m=1;k.KDC(5,t);k.KO(-1,t," ");k.KIO(-1,this.s23,2,t);k.KO(-1,t,"ما ");}else if(k.KFCM(4,t,['ط','ت','ّ','ت'])){m=1;k.KDC(4,t);k.KO(-1,t,"طّ");}else if(k.KFCM(4,t,['ص','س','ّ','س'])){m=1;k.KDC(4,t);k.KO(-1,t,"صّ");}else if(k.KFCM(4,t,['ض','د','ّ','د'])){m=1;k.KDC(4,t);k.KO(-1,t,"ضّ");}else if(k.KFCM(4,t,['خ','ك','ّ','ك'])){m=1;k.KDC(4,t);k.KO(-1,t,"خّ");}else if(k.KFCM(4,t,[{t:'a',a:this.s20},'س','و','ت'])){m=1;k.KDC(4,t);k.KO(-1,t,"سُبحَانَهُ وَتَعَالَىٰ");}else if(k.KFCM(4,t,[{t:'a',a:this.s20},'ت','و','ت'])){m=1;k.KDC(4,t);k.KO(-1,t,"تَبَارَكَ وَتَعَالَىٰ");}else if(k.KFCM(4,t,[{t:'a',a:this.s20},'ص','َ','و'])){m=1;k.KDC(4,t);k.KO(-1,t,"صَلَّى ٱللَّٰهُ عَلَيهِۦ وَسَلَّمَ");}else if(k.KFCM(4,t,[{t:'a',a:this.s20},'س','َ','و'])){m=1;k.KDC(4,t);k.KO(-1,t,"صَلَّى ٱللَّٰهُ عَلَيهِۦ وَسَلَّمَ");}else if(k.KFCM(4,t,[{t:'a',a:this.s11},'أ','ُ','ُ'])){m=1;k.KDC(4,t);k.KIO(-1,this.s11,1,t);k.KO(-1,t,"ؤُ");}else if(k.KFCM(4,t,[{t:'a',a:this.s16},'ل','ْ','َ'])){m=1;k.KDC(4,t);k.KO(-1,t,"الأ");}else if(k.KFCM(4,t,[{t:'a',a:this.s16},'ل','ْ','ُ'])){m=1;k.KDC(4,t);k.KO(-1,t,"الأُ");}else if(k.KFCM(4,t,[{t:'a',a:this.s16},'ل','ْ','ِ'])){m=1;k.KDC(4,t);k.KO(-1,t,"الإ");}else if(k.KFCM(4,t,[{t:'n'},'ل','ْ',{t:'a',a:this.s11}])){m=1;k.KDC(3,t);k.KO(-1,t,"لل");k.KIO(-1,this.s11,4,t);k.KO(-1,t,"ّ");}else if(k.KFCM(4,t,[' ','ل','ْ',{t:'a',a:this.s11}])){m=1;k.KDC(4,t);k.KO(-1,t," لل");k.KIO(-1,this.s11,4,t);k.KO(-1,t,"ّ");}else if(k.KFCM(4,t,[{t:'a',a:this.s23},'َ','ْ',{t:'a',a:this.s11}])){m=1;k.KDC(4,t);k.KIO(-1,this.s23,1,t);k.KO(-1,t,"ال");k.KIO(-1,this.s11,4,t);k.KO(-1,t,"ّ");}else if(k.KFCM(4,t,[{t:'a',a:this.s23},'ِ','ْ',{t:'a',a:this.s11}])){m=1;k.KDC(4,t);k.KIO(-1,this.s23,1,t);k.KO(-1,t,"ِال");k.KIO(-1,this.s11,4,t);k.KO(-1,t,"ّ");}else if(k.KFCM(4,t,['ا','ل','ّ','ل'])){m=1;k.KDC(4,t);k.KO(-1,t,"اللّ");}else if(k.KFCM(4,t,['ب','ِ','ل','ْ'])){m=1;k.KDC(4,t);k.KO(-1,t,"بِال");}else if(k.KFCM(4,t,['ب','ِ','ْ',{t:'a',a:this.s11}])){m=1;k.KDC(4,t);k.KO(-1,t,"بِال");k.KIO(-1,this.s11,4,t);k.KO(-1,t,"ّ");}else if(k.KFCM(4,t,[{t:'n'},'أ','ل',{t:'a',a:this.s11}])){m=1;k.KDC(3,t);k.KO(-1,t,"ال");k.KIO(-1,this.s11,4,t);}else if(k.KFCM(4,t,[' ','أ','ل',{t:'a',a:this.s11}])){m=1;k.KDC(4,t);k.KO(-1,t," ال");k.KIO(-1,this.s11,4,t);}else if(k.KFCM(4,t,[{t:'n'},'أ','ل',{t:'a',a:this.s12}])){m=1;k.KDC(3,t);k.KO(-1,t,"ال");k.KIO(-1,this.s12,4,t);}else if(k.KFCM(4,t,[' ','أ','ل',{t:'a',a:this.s12}])){m=1;k.KDC(4,t);k.KO(-1,t," ال");k.KIO(-1,this.s12,4,t);}else if(k.KFCM(4,t,['ؤ','ُ','و','ْ'])){m=1;k.KDC(4,t);k.KO(-1,t,"ؤوا");}else if(k.KFCM(4,t,[{t:'a',a:this.s11},{t:'a',a:this.s15},'و','ْ'])){m=1;k.KDC(4,t);k.KIO(-1,this.s11,1,t);k.KO(-1,t,"وا");}else if(k.KFCM(4,t,['ى','َ','ن','ْ'])){m=1;k.KDC(4,t);k.KO(-1,t,"ىً");}else if(k.KFCM(4,t,['ت','َ','ن','ْ'])){m=1;k.KDC(4,t);k.KO(-1,t,"تًا");}else if(k.KFCM(4,t,[{t:'a',a:this.s11},'َ','ن','ْ'])){m=1;k.KDC(4,t);k.KIO(-1,this.s11,1,t);k.KO(-1,t,"ًا");}else if(k.KFCM(4,t,['ئ','َ','ن','ْ'])){m=1;k.KDC(4,t);k.KO(-1,t,"ئًا");}else if(k.KFCM(4,t,['و','َ','ن','ْ'])){m=1;k.KDC(4,t);k.KO(-1,t,"وًا");}else if(k.KFCM(4,t,['ي','َ','ن','ْ'])){m=1;k.KDC(4,t);k.KO(-1,t,"يًا");}else if(k.KFCM(4,t,[{t:'n'},'ل','َ',{t:'a',a:this.s24}])){m=1;k.KDC(3,t);k.KO(-1,t,"لا");k.KIO(-1,this.s24,4,t);}else if(k.KFCM(4,t,[' ','ل','َ',{t:'a',a:this.s24}])){m=1;k.KDC(4,t);k.KO(-1,t," لا");k.KIO(-1,this.s24,4,t);}else if(k.KFCM(4,t,[{t:'n'},'ف','ِ',' '])){m=1;k.KDC(3,t);k.KO(-1,t,"في ");}else if(k.KFCM(4,t,[' ','ف','ِ',' '])){m=1;k.KDC(4,t);k.KO(-1,t," في ");}else if(k.KFCM(4,t,[{t:'n'},'م','َ',' '])){m=1;k.KDC(3,t);k.KO(-1,t,"ما ");}else if(k.KFCM(4,t,[' ','م','َ',' '])){m=1;k.KDC(4,t);k.KO(-1,t," ما ");}else if(k.KFCM(4,t,[{t:'a',a:this.s25},{t:'a',a:this.s15},'ه','ه'])){m=1;k.KDC(4,t);k.KIO(-1,this.s26,1,t);k.KIO(-1,this.s15,2,t);}else if(k.KFCM(4,t,[{t:'a',a:this.s26},{t:'i',i:this.s25,o:1},'ه','ه'])){m=1;k.KDC(4,t);k.KIO(-1,this.s26,1,t);k.KO(-1,t,"ّ");}if(m) {}else if(k.KFCM(3,t,[{t:'a',a:this.s20},'ج','ج'])){m=1;k.KDC(3,t);k.KO(-1,t,"جَلَّ جَلَالُه");}else if(k.KFCM(3,t,[{t:'a',a:this.s20},'ر','َ'])){m=1;k.KDC(3,t);k.KO(-1,t,"رَضِيَ ٱللَّٰهُ عَنهُ");}else if(k.KFCM(3,t,['أ','َ','َ'])){m=1;k.KDC(3,t);k.KO(-1,t,"ى");}else if(k.KFCM(3,t,['أ','َ',{t:'a',a:this.s11}])){m=1;k.KDC(3,t);k.KO(-1,t,"أ");k.KIO(-1,this.s11,3,t);}else if(k.KFCM(3,t,['أ','َ',{t:'a',a:this.s12}])){m=1;k.KDC(3,t);k.KO(-1,t,"أ");k.KIO(-1,this.s12,3,t);}else if(k.KFCM(3,t,['و','ُ','ُ'])){m=1;k.KDC(3,t);k.KO(-1,t,"ُؤ");}else if(k.KFCM(3,t,['ء','ُ','ُ'])){m=1;k.KDC(3,t);k.KO(-1,t,"ؤُ");}else if(k.KFCM(3,t,['ي','ِ','ِ'])){m=1;k.KDC(3,t);k.KO(-1,t,"إ");}else if(k.KFCM(3,t,[{t:'n'},'ْ','ْ'])){m=1;k.KDC(2,t);k.KO(-1,t,"ا");}else if(k.KFCM(3,t,[' ','ْ','ْ'])){m=1;k.KDC(3,t);k.KO(-1,t," ا");}else if(k.KFCM(3,t,['ئ','ْ','َ'])){m=1;k.KDC(3,t);k.KO(-1,t,"أ");}else if(k.KFCM(3,t,['ئ','َ','ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"أ");}else if(k.KFCM(3,t,['ؤ','َ','ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"أ");}else if(k.KFCM(3,t,['ؤ','ْ','َ'])){m=1;k.KDC(3,t);k.KO(-1,t,"أ");}else if(k.KFCM(3,t,['ئ','ْ','ِ'])){m=1;k.KDC(3,t);k.KO(-1,t,"إ");}else if(k.KFCM(3,t,['ئ','ِ','ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"إ");}else if(k.KFCM(3,t,[{t:'n'},'ْ',{t:'a',a:this.s11}])){m=1;k.KDC(2,t);k.KO(-1,t,"ال");k.KIO(-1,this.s11,3,t);k.KO(-1,t,"ّ");}else if(k.KFCM(3,t,[' ','ْ',{t:'a',a:this.s11}])){m=1;k.KDC(3,t);k.KO(-1,t," ال");k.KIO(-1,this.s11,3,t);k.KO(-1,t,"ّ");}else if(k.KFCM(3,t,[{t:'a',a:this.s23},'ْ',{t:'a',a:this.s11}])){m=1;k.KDC(3,t);k.KIO(-1,this.s23,1,t);k.KO(-1,t,"ال");k.KIO(-1,this.s11,3,t);k.KO(-1,t,"ّ");}else if(k.KFCM(3,t,['ل','ء','َ'])){m=1;k.KDC(3,t);k.KO(-1,t,"الأ");}else if(k.KFCM(3,t,['ل','ء','ُ'])){m=1;k.KDC(3,t);k.KO(-1,t,"الأُ");}else if(k.KFCM(3,t,['ل','ء','ِ'])){m=1;k.KDC(3,t);k.KO(-1,t,"الإ");}else if(k.KFCM(3,t,['و','َ','ُ'])){m=1;k.KDC(3,t);k.KO(-1,t,"وَأُ");}else if(k.KFCM(3,t,['و','َ','ِ'])){m=1;k.KDC(3,t);k.KO(-1,t,"وَإ");}else if(k.KFCM(3,t,['أ','ُ','ِ'])){m=1;k.KDC(3,t);k.KO(-1,t,"إ");}else if(k.KFCM(3,t,['ء','َ','َ'])){m=1;k.KDC(3,t);k.KO(-1,t,"أ");}else if(k.KFCM(3,t,['ء','ِ','ِ'])){m=1;k.KDC(3,t);k.KO(-1,t,"إ");}else if(k.KFCM(3,t,[{t:'a',a:this.s11},'و','ْ'])){m=1;k.KDC(3,t);k.KIO(-1,this.s11,1,t);k.KO(-1,t,"وا");}else if(k.KFCM(3,t,['ء','و','ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"ءوا");}else if(k.KFCM(3,t,['ؤ','و','ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"ؤوا");}else if(k.KFCM(3,t,['َ','ن','ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"ً");}else if(k.KFCM(3,t,['ُ','ن','ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"ٌ");}else if(k.KFCM(3,t,['ِ','ن','ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"ٍ");}else if(k.KFCM(3,t,[{t:'a',a:this.s11},'ا','ً'])){m=1;k.KDC(3,t);k.KIO(-1,this.s11,1,t);k.KO(-1,t,"ًا");}else if(k.KFCM(3,t,['ي','ا','ً'])){m=1;k.KDC(3,t);k.KO(-1,t,"يًا");}else if(k.KFCM(3,t,['و','ا','ً'])){m=1;k.KDC(3,t);k.KO(-1,t,"وًا");}else if(k.KFCM(3,t,['ت',{t:'a',a:this.s15},'ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"ة");k.KIO(-1,this.s15,2,t);}else if(k.KFCM(3,t,['ت',{t:'a',a:this.s14},'ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"ة");k.KIO(-1,this.s14,2,t);}else if(k.KFCM(3,t,['ت','ً','ْ'])){m=1;k.KDC(3,t);k.KO(-1,t,"تًا");}else if(k.KFCM(3,t,[{t:'a',a:this.s25},'ه','ه'])){m=1;k.KDC(3,t);k.KIO(-1,this.s26,1,t);}else if(k.KFCM(3,t,['ت','ّ','ت'])){m=1;k.KDC(3,t);k.KO(-1,t,"ط");}else if(k.KFCM(3,t,['س','ّ','س'])){m=1;k.KDC(3,t);k.KO(-1,t,"ص");}else if(k.KFCM(3,t,['د','ّ','د'])){m=1;k.KDC(3,t);k.KO(-1,t,"ض");}else if(k.KFCM(3,t,['ك','ّ','ك'])){m=1;k.KDC(3,t);k.KO(-1,t,"خ");}else if(k.KFCM(3,t,['ض','ّ','ض'])){m=1;k.KDC(3,t);k.KO(-1,t,"ظ");}else if(k.KFCM(3,t,['ه','ّ','ه'])){m=1;k.KDC(3,t);k.KO(-1,t,"ح");}else if(k.KFCM(2,t,[{t:'a',a:this.s11},{t:'i',i:this.s11,o:1}])){m=1;k.KDC(2,t);k.KIO(-1,this.s11,1,t);k.KO(-1,t,"ّ");}else if(k.KFCM(2,t,['و','و'])){m=1;k.KDC(2,t);k.KO(-1,t,"وّ");}else if(k.KFCM(2,t,['ي','ي'])){m=1;k.KDC(2,t);k.KO(-1,t,"يّ");}else if(k.KFCM(2,t,[{t:'a',a:this.s21},'/'])){m=1;k.KDC(2,t);k.KIO(-1,this.s22,1,t);}else if(k.KFCM(2,t,[{t:'a',a:this.s21},'ۚ'])){m=1;k.KDC(2,t);k.KIO(-1,this.s22,1,t);}else if(k.KFCM(2,t,['َ','َ'])){m=1;k.KDC(2,t);k.KO(-1,t,"ا");}else if(k.KFCM(2,t,['ا','َ'])){m=1;k.KDC(2,t);k.KO(-1,t,"أ");}else if(k.KFCM(2,t,['ى','َ'])){m=1;k.KDC(2,t);k.KO(-1,t,"ٰ");}else if(k.KFCM(2,t,['ٰ','َ'])){m=1;k.KDC(2,t);k.KO(-1,t,"آ");}else if(k.KFCM(2,t,['َ','ى'])){m=1;k.KDC(2,t);k.KO(-1,t,"ى");}else if(k.KFCM(2,t,['ُ','ُ'])){m=1;k.KDC(2,t);k.KO(-1,t,"و");}else if(k.KFCM(2,t,['ِ','ِ'])){m=1;k.KDC(2,t);k.KO(-1,t,"ي");}else if(k.KFCM(2,t,['ا','ِ'])){m=1;k.KDC(2,t);k.KO(-1,t,"إ");}else if(k.KFCM(2,t,['ْ','ْ'])){m=1;k.KDC(2,t);k.KO(-1,t,"ّ");}else if(k.KFCM(2,t,['ّ','ْ'])){m=1;k.KDC(2,t);k.KO(-1,t,"ٓ");}else if(k.KFCM(2,t,['ٓ','ْ'])){m=1;k.KDC(2,t);k.KO(-1,t,"ـ");}else if(k.KFCM(2,t,['ء','ء'])){m=1;k.KDC(2,t);k.KO(-1,t,"ئ");}else if(k.KFCM(2,t,['أ','ء'])){m=1;k.KDC(2,t);k.KO(-1,t,"ئ");}else if(k.KFCM(2,t,['ُ','ء'])){m=1;k.KDC(2,t);k.KO(-1,t,"ُؤ");}else if(k.KFCM(2,t,['َ','ء'])){m=1;k.KDC(2,t);k.KO(-1,t,"َأ");}else if(k.KFCM(2,t,['ِ','ء'])){m=1;k.KDC(2,t);k.KO(-1,t,"ِئ");}else if(k.KFCM(2,t,['\'','\''])){m=1;k.KDC(2,t);k.KO(-1,t,"\"");}else if(k.KFCM(2,t,[{t:'n'},'ُ'])){m=1;k.KDC(1,t);k.KO(-1,t,"أُ");}else if(k.KFCM(2,t,[{t:'n'},'َ'])){m=1;k.KDC(1,t);k.KO(-1,t,"أ");}else if(k.KFCM(2,t,[{t:'n'},'ِ'])){m=1;k.KDC(1,t);k.KO(-1,t,"إ");}else if(k.KFCM(2,t,[' ','ُ'])){m=1;k.KDC(2,t);k.KO(-1,t," أُ");}else if(k.KFCM(2,t,[' ','َ'])){m=1;k.KDC(2,t);k.KO(-1,t," أ");}else if(k.KFCM(2,t,[' ','ِ'])){m=1;k.KDC(2,t);k.KO(-1,t," إ");}else if(k.KFCM(2,t,['أ','ِ'])){m=1;k.KDC(2,t);k.KO(-1,t,"إ");}else if(k.KFCM(2,t,['إ','َ'])){m=1;k.KDC(2,t);k.KO(-1,t,"أ");}else if(k.KFCM(2,t,['أ','ْ'])){m=1;k.KDC(2,t);k.KO(-1,t,"آ");}else if(k.KFCM(2,t,['ت','ْ'])){m=1;k.KDC(2,t);k.KO(-1,t,"ة");}else if(k.KFCM(2,t,['ه','ْ'])){m=1;k.KDC(2,t);k.KO(-1,t,"ة");}else if(k.KFCM(2,t,[{t:'a',a:this.s27},'/'])){m=1;k.KDC(2,t);k.KIO(-1,this.s28,1,t);}else if(k.KFCM(2,t,[{t:'a',a:this.s29},'/'])){m=1;k.KDC(2,t);k.KIO(-1,this.s30,1,t);}else if(k.KFCM(2,t,[{t:'a',a:this.s31},'/'])){m=1;k.KDC(2,t);k.KIO(-1,this.s32,1,t);}else if(k.KFCM(2,t,[{t:'a',a:this.s33},'/'])){m=1;k.KDC(2,t);k.KIO(-1,this.s34,1,t);}else if(k.KFCM(2,t,[{t:'a',a:this.s35},'/'])){m=1;k.KDC(2,t);k.KIO(-1,this.s36,1,t);}else if(k.KFCM(2,t,[{t:'a',a:this.s37},{t:'a',a:this.s37}])){m=1;k.KDC(2,t);k.KIO(-1,this.s37,2,t);}else if(k.KFCM(1,t,[';'])){m=1;k.KDC(1,t);k.KO(-1,t,"؛");}else if(k.KFCM(1,t,['?'])){m=1;k.KDC(1,t);k.KO(-1,t,"؟");}else if(k.KFCM(1,t,[','])){m=1;k.KDC(1,t);k.KO(-1,t,"،");}return r;};}